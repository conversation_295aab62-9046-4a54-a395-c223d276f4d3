<template>
  <section  >
    <a-card size="small" title="客户结算" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '160px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.businessType2"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="accountNo" :label="'结算单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.accountNo"/>
          </a-form-item>
          <a-form-item name="purchaseOrderNo" :label="'进货单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.purchaseOrderNo"/>
          </a-form-item>
          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.contractNo"/>
          </a-form-item>
          <a-form-item name="customer" :label="'客户'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.customer" id="customer">
              <a-select-option class="cs-select-dropdown" v-for="item in supplierList"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.curr"
              :disabled="true"
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currMap"
            ></a-select>
          </a-form-item>
          <a-form-item name="exchangeRate" :label="'出运数量'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.exchangeRate" notConvertNumber decimal int-length="13" precision="6"/>
          </a-form-item>
          <a-form-item name="goodsPrice" :label="'货款'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            @change="allChange"
                            v-model:value="formData.goodsPrice" notConvertNumber decimal int-length="14" precision="5"/>
          </a-form-item>
          <a-form-item name="agentFeeRate" :label="'代理费率%'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.agentFeeRate"
                            :formatter="value => `${value}%`"
                            :parser="value => value.replace('%', '')"
                            @change="allChange"
                            notConvertNumber decimal int-length="15" precision="4"/>
          </a-form-item>
          <a-form-item name="agentFeeTotal" :label="'代理费'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="true" size="small"
                            :formatter="value => {
                              if (!value) return '0';
                              const parts = value.toString().split('.');
                              parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                              return parts.join('.');}"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.agentFeeTotal" notConvertNumber decimal int-length="17" precision="2"/>
          </a-form-item>
          <a-form-item name="businessDate" :label="'业务日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable || formData.status  !== '0' "
              v-model:value="formData.businessDate"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>
          <a-form-item name="gName" :label="'商品类别'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.gName"/>
          </a-form-item>
          <a-form-item name="sendFinance" :label="'发送财务系统'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable || formData.status  !== '0' "  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendFinance" id="destinationPort">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="note" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable || formData.status  !== '0' " size="small" v-model:value="formData.note"/>
          </a-form-item>
          <a-form-item name="redFlush" :label="'是否红冲'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.redFlush" id="destinationPort">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isNot"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
                {{item.value}} {{ item.label  }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="status" :label="'数据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.confirmTime"
              id="confirmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>

          <a-form-item name="createrUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createrUserName"/>
          </a-form-item>
          <!--          制单时间-->
          <a-form-item name="createrTime" :label="'制单时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.createrTime"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>
          </a-form-item>


          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right" v-if="!showDisable" :loading="handlerSaveLoading"
                      v-show="props.editConfig.editStatus !== 'SHOW' " :disabled="formData.status  !== '0' ">保存
            </a-button>
<!--            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存-->
<!--            </a-button>-->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
<!--            <div class="cs-action-btn-item" v-has="['yc-cs:warehouse-plan:confirm']">-->
<!--              <a-button size="small" :loading="confirmLoading" @click="handlerConfirm" v-if="!showDisable"  v-show="props.editConfig.editStatus !== 'SHOW' ">-->
<!--                <template #icon>-->
<!--                  <GlobalIcon type="check" style="color:green"/>-->
<!--                </template>-->
<!--                确认-->
<!--              </a-button>-->
<!--            </div>-->
<!--            <div class="cs-action-btn-item" v-has="['yc-cs:warehouse-plan:redFlush']">-->
<!--              <a-button size="small" :loading="redFlushLoading" @click="handlerRedFlush" v-if="!showDisable"  v-show="props.editConfig.editStatus !== 'SHOW' ">-->
<!--                <template #icon>-->
<!--                  <GlobalIcon type="up" style="color:red"/>-->
<!--                </template>-->
<!--                红冲-->
<!--              </a-button>-->
<!--            </div>-->
          </div>
        </a-form>
      </div>
    </a-card>

  </section>
</template>

<script setup>
import {editStatus,productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import {usePCode} from "@/view/common/usePCode";
import {getOrderSupplierList, insertStoreIHeadList, updateStoreIHeadList,confirmStoreIHead,redFlushStoreIHead} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
import 'vue-multiselect/dist/vue-multiselect.min.css';
import CsSelect from "@/components/select/CsSelect.vue";
import ycCsApi from "@/api/ycCsApi";
import BizStoreIListList from "@/view/warehouse/storeIHead/list/BizStoreIListList";
import useEventBus from "@/view/common/eventBus";
import {insertCustomerAccount, updateCustomerAccount} from "@/api/payment/payment_info";
const { getPCode } = usePCode()

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});
const confirmLoading = ref(false)
const redFlushLoading = ref(false)
const handlerSaveLoading = ref(false)
const {emitEvent} = useEventBus()

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  emit('onEditBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  sid:'',
  businessType:'9',
  accountNo: '',
  contractNo: '',
  purchaseOrderNo: '',
  customer: '',
  curr: '',
  exchangeRate: '',
  goodsPrice: '',
  agentFeeRate: '',
  agentFee: '',
  agentTaxFee: '',
  agentFeeTotal: '',
  businessDate: '',
  gName: '',
  producrSome: '',
  sendFinance: '0',
  redFlush: '1',
  note: '',
  status: '0',
  createrBy: '',
  createrUserName: '',
  createrTime: '',

})
// 校验规则
const rules = {

  businessType: [
    { required: true, message: '业务类型不能为空！', trigger: 'blur' },
  ],
  accountNo: [
    { max: 80, message: '结算单号长度不能超过60位字节', trigger: 'blur'},
    { required: true, message: '结算单号不能为空！', trigger: 'blur' },
  ],
  contractNo: [
    { max: 80, message: '合同号长度不能超过60位字节', trigger: 'blur'},
    { required: true, message: '合同号不能为空！', trigger: 'blur' },
  ],
  purchaseOrderNo: [
    { max: 80, message: '进货单号长度不能超过60位字节', trigger: 'blur'},
    // { required: true, message: '进货单号不能为空！', trigger: 'blur' },
  ],
  customer: [
    { required: true, message: '客户不能为空！', trigger: 'blur' },
  ],
  exchangeRate: [
    { required: true, message: '汇率不能为空！', trigger: 'blur' },
  ],
  goodsPrice: [
    { required: true, message: '货款不能为空！', trigger: 'blur' },
  ],
  curr: [
    { required: true, message: '币种不能为空！', trigger: 'blur' },
  ],
  agentFeeRate: [
    { required: true, message: '代理费率%不能为空！', trigger: 'blur' },
  ],
  // agentFee: [
  //   { required: true, message: '代理费（不含税金额）不能为空！', trigger: 'blur' },
  // ],
  // agentTaxFee: [
  //   { required: true, message: '代理费税额不能为空！', trigger: 'blur' },
  // ],
  agentFeeTotal: [
    { required: true, message: '代理费不能为空！', trigger: 'blur' },
  ],
  gName: [
    { required: true, message: '商品名称不能为空！', trigger: 'blur' },
  ],
  producrSome: [
    { required: true, message: '商品与数量不能为空！', trigger: 'blur' },
  ],
  redFlush: [
    { required: true, message: '是否红冲不能为空！', trigger: 'blur' },
  ],
  sendFinance: [
    { required: true, message: '发送财务系统不能为空！', trigger: 'blur' },
  ],
  businessDate: [
    { required: true, message: '业务日期不能为空！', trigger: 'blur' },
  ],
  status: [
    { required: true, message: '单据状态不能为空！', trigger: 'blur' },
  ],
  note: [
    {max: 200, message: '备注长度不能超过200位字节', trigger: 'blur'}
  ],
  createrUserName: [
    {required: true, message: '制单人不能为空！', trigger: 'blur'}
  ],
  createrTime: [
    {required: true, message: '制单时间不能为空！', trigger: 'blur'}
  ]

}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getSupplierList();
  getPCode().then(res=>{
    pCode.value = res;
    currMap.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      value
    }));
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    Object.assign(formData, {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
});

const getSupplierList  = () =>{
  getOrderSupplierList({}).then(res=>{
    // console.log('获取供应商信息未',res)
    if (!isNullOrEmpty(res.data)){
      supplierList.value = res.data
    }
  })
}
const supplierList = ref([])
const currMap = ref([])
// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      handlerSaveLoading.value = true
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertCustomerAccount(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            handlerSaveLoading.value = false
            // onBack(true)
          }else {
            message.error(res.message)
            handlerSaveLoading.value = false
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        console.log('value',formData)
        updateCustomerAccount(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            handlerSaveLoading.value = false
            Object.assign(formData, res.data)
            formData.createrUserName = res.data.updateUserName
            formData.createrTime = res.data.updateTime
            onBack({
              editData: res.data,
              showBody: true,
              editStatus: editStatus.EDIT
            })
            emitEvent('refreshOrderList')
            // onBack(true)
          }else {
            handlerSaveLoading.value = false
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};



const allChange = () =>{
  agentFeeTotalChange();
}
const agentFeeTotalChange = () =>{
  if(formData.agentFeeTotal !== null){
    delete formData.agentFeeTotal;
  }
  if(formData.goodsPrice !== null && formData.agentFeeRate !== null){
    formData.agentFeeTotal = agentFeeTotalCount(formData)
  }
}

const agentFeeTotalCount = (row) => {
  const goodsPrice = parseFloat(row.goodsPrice);
  const agentFeeRate = parseFloat(row.agentFeeRate);
  const agentFeeTotal = roundToDecimal(goodsPrice*agentFeeRate/100,2)
  return agentFeeTotal !== null ? agentFeeTotal : null
};

function roundToDecimal(num, decimals) {
  const factor = Math.pow(10, decimals);
  return Math.round(num * factor) / factor;
}

</script>

<style lang="less" scoped>


</style>



