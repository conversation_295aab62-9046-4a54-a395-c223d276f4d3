<template>
  <a-modal
    v-model:visible="props.visible"
    title="预警设置"
    width="600px"
    :maskClosable="false"
    @cancel="handleCancel"
    :keyboard="false"
    :confirmLoading="loading"
    :footer="null"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      :rules="rules"
    >
      <a-form-item label="预计收款时间" name="estReceiveDate">
        <a-date-picker
          v-model:value="formData.estReceiveDate"
          placeholder="请选择预计收款时间"
          style="width: 100%"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
        />
      </a-form-item>

      <a-form-item label="预计付款时间" name="estPaymentDate">
        <a-date-picker
          v-model:value="formData.estPaymentDate"
          placeholder="请选择预计付款时间"
          style="width: 100%"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
        />
      </a-form-item>

      <a-form-item label="预计裁定时间" name="estArbitrationDate">
        <a-date-picker
          v-model:value="formData.estArbitrationDate"
          placeholder="请选择预计裁定时间"
          style="width: 100%"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
        />
      </a-form-item>

      <a-form-item label="预计许可证申办时间" name="estLicenseDate">
        <a-date-picker
          v-model:value="formData.estLicenseDate"
          placeholder="请选择预计许可证申办时间"
          style="width: 100%"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
        />
      </a-form-item>

      <a-form-item label="预计准运证申办时间" name="estTransportCertDate">
        <a-date-picker
          v-model:value="formData.estTransportCertDate"
          placeholder="请选择预计准运证申办时间"
          style="width: 100%"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
        />
      </a-form-item>

      <a-form-item label="预计保险申办时间" name="estInsuranceDate">
        <a-date-picker
          v-model:value="formData.estInsuranceDate"
          placeholder="请选择预计保险申办时间"
          style="width: 100%"
          format="YYYY-MM-DD"
          valueFormat="YYYY-MM-DD"
        />
      </a-form-item>
    </a-form>
    <div style="text-align: center; margin-top: 24px;">
      <a-button type="primary" @click="handleOk" :loading="loading" style="margin-right: 12px;">
        保存
      </a-button>
      <a-button @click="handleCancel">
        返回
      </a-button>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { getWarningSettings, saveWarningSettings } from '@/api/equipment/equipmentPlanApi';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  planId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:visible', 'success']);

const formRef = ref();
const loading = ref(false);

// 表单数据
const formData = reactive({
  estReceiveDate: null,
  estPaymentDate: null,
  estArbitrationDate: null,
  estLicenseDate: null,
  estTransportCertDate: null,
  estInsuranceDate: null
});

// 表单验证规则
const rules = {
  // 可以根据需要添加验证规则
};

// 重置表单数据
const resetFormData = () => {
  formData.estReceiveDate = null;
  formData.estPaymentDate = null;
  formData.estArbitrationDate = null;
  formData.estLicenseDate = null;
  formData.estTransportCertDate = null;
  formData.estInsuranceDate = null;
};

// 获取预警设置数据
const getWarningData = async () => {
  if (!props.planId) return;

  try {
    loading.value = true;
    const res = await getWarningSettings(props.planId);
    if (res.code === 200 && res.data) {
      Object.assign(formData, res.data);
    }
  } catch (error) {
    console.error('获取预警设置失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理确定按钮
const handleOk = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    const params = {
      planId: props.planId,
      ...formData
    };

    const res = await saveWarningSettings(params);
    if (res.code === 200) {
      message.success('预警设置保存成功');
      emit('success'); // 触发成功事件，用于刷新父页面数据
      handleCancel();
    } else {
      message.error(res.message || '保存失败');
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理取消按钮
const handleCancel = () => {
  resetFormData();
  formRef.value?.resetFields();
  emit('update:visible', false);
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.planId) {
    nextTick(() => {
      getWarningData();
    });
  }
});
</script>

<style lang="less" scoped>
.ant-form-item {
  margin-bottom: 16px;
}
</style>
