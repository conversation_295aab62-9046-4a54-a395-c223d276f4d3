<template>
  <section  >
    <a-card size="small" title="进口费用表头" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
<!--          单据号-->
          <a-form-item name="documentNumber" :label="'单据号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.documentNumber"/>
          </a-form-item>
<!--          业务类型-->
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable||showCostDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in productClassify.businessType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          预付标志-->
          <a-form-item name="advanceFlag" :label="'预付标志'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable||showCostDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.advanceFlag" id="advanceFlag">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          部门-->
          <a-form-item name="deptName" :label="'部门'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.deptName"/>
          </a-form-item>
<!--          币种-->
          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable||showCostDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.curr" id="curr">
              <a-select-option v-for="item in typeMessage.currTypeOptions"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          收款方-->
          <a-form-item name="payee" :label="'收款方'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable||showCostDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.payee" id="payee">
              <a-select-option v-for="item in typeMessage.buyerOptions"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          发送用友-->
          <a-form-item name="sendUfida" :label="'发送财务系统'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable||showCostDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendUfida" id="sendUfida">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          备注-->
          <a-form-item name="remark" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable||showCostDisable" size="small" v-model:value="formData.remark"/>
          </a-form-item>
<!--          单据状态-->
          <a-form-item name="state" :label="'单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.state" id="state">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          制单人-->
          <a-form-item name="createUser" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createUser"/>
          </a-form-item>
<!--          制单日期-->
          <a-form-item name="createUserTime" :label="'制单日期'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createUserTime"/>
          </a-form-item>
<!--          确认时间-->
          <a-form-item name="confirmationTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.confirmationTime"/>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <div style="display: flex;">
              <a-button size="small" :loading="iconLoading" type="primary" @click="handlerSave" class="cs-margin-right"
                        v-show="props.editConfig.editStatus !== 'show' ">保存
              </a-button>
              <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            </div>
          </div>
        </a-form>
        <cost-list-message @save="handlerSave" v-if="props.editConfig.editStatus != 'add'" :editConfig="editConfig" :typeMessage="typeMessage"/>
      </div>
    </a-card>
  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {insertCostHead, updateCostHead} from "@/api/cost/cost_message_info";
import {localeContent} from "@/view/utils/commonUtil";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
const { getPCode } = usePCode()
import CostListMessage from "@/view/costManage/costIManage/list/CostListMessage.vue";
import {
  getUserInfo
} from "@/api/payment/payment_info";
import date from "view-ui-plus/src/utils/date";
import BizISellListList from "@/view/dec/imported_cigarettes/sell/list/BizISellListList.vue";


const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  typeMessage: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack','onShowTap']);

const onBack = (val) => {
  console.log('返回', val);
  emit('onEditBack', val);
};

// 是否禁用
const showDisable = ref(false)
const showCostDisable = ref(false)
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const pad = (num) => num.toString().padStart(2, '0');

  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ` +
    `${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
};
// 表单数据
const formData = reactive({
  sid:'',
  documentNumber: '',
  businessType:'1',
  deptName: '业务一部',
  advanceFlag: '0',
  curr: '142',
  payee: '',
  sendUfida: '0',
  remark: '',
  state: '0',
  createUser: '',
  createUserTime: formatDate(new Date()),
  confirmationTime: ''
})
// 校验规则
const rules = {
  createUser:[
    {required: true}
  ],
  createUserTime:[
    {required: true}
  ],
  businessType: [
    {required: true, message: '业务类型不能为空', trigger: 'blur'},
    {max: 60, message: '业务类型长度不能超过60位字节', trigger: 'blur'}
  ],
  advanceFlag: [
    {required: true, message: '预付标志不能为空', trigger: 'blur'},
    {max: 10, message: '预付标志长度不能超过10位字节', trigger: 'blur'}
  ],
  deptName: [
    {required: true, message: '部门不能为空', trigger: 'blur'},
    {max: 60, message: '部门长度不能超过60位字节', trigger: 'blur'}
  ],
  curr: [
    {required: true, message: '币种不能为空', trigger: 'blur'},
    {max: 10, message: '币种长度不能超过10位字节', trigger: 'blur'}
  ],
  payee: [
    {required: true, message: '收款方不能为空', trigger: 'blur'},
    {max: 200, message: '收款方长度不能超过200位字节', trigger: 'blur'}
  ],
  sendUfida: [
    {max: 10, message: '发送用友长度不能超过3位字节', trigger: 'blur'}
  ],
  remark: [
    {max: 200, message: '备注长度不能超过200位字节', trigger: 'blur'}
  ],
  state: [
    {max: 10, message: '单据状态长度不能超过10位字节', trigger: 'blur'}
  ]
}
const getUpdateUser = () => {
  getUserInfo().then((res)=>{
    if (res.data !=null){
      formData.createUser =  res.data.userName
    }
  })
}
const pCode = ref('')
const iconLoading = ref(false);
// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  console.log(props.editConfig)
  console.log(props.editConfig.editStatus)
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD.valueOf()) {
    showDisable.value = false
    Object.assign(formData, {});
    getUpdateUser()
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT.valueOf()) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW.valueOf()) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  if (props.editConfig && props.editConfig.editStatus !== editStatus.ADD.valueOf()) {
    if(props.editConfig.editData != null){
      if(props.editConfig.editData.state === '1' || props.editConfig.editData.state === '2'){
        showCostDisable.value = true;
      }else{
        showCostDisable.value = false;
      }
    }
  }



});



// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  if(showCostDisable.value === true){
    return message.warning('确认状态无法编辑！')
  }
  formRef.value
    .validate()
    .then(() => {
      iconLoading.value = true;
      if (props.editConfig && props.editConfig.editStatus === 'add'){
        insertCostHead(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            Object.assign(formData, res.data)
            props.editConfig.editStatus = editStatus.EDIT;
            // Object.assign(props.editConfig.editData, res.data)
            emit('onShowTap', res.data);
            iconLoading.value = false;
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === 'edit'){
        console.log('value',formData)
        updateCostHead(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            Object.assign(formData, res.data)
            Object.assign(props.editConfig.editData, res.data)
            message.success('表头保存成功!')
            iconLoading.value = false;
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

</script>

<style lang="less" scoped>

</style>



