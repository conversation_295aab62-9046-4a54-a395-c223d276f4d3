<template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search" >
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search" style="padding-bottom: 10px;padding-top: 10px">
            <div v-show="showSearch">
              <ContractSearch ref="headSearch"  :businessType="props.editConfig.editData.businessType" :headId="props.editConfig.editData.sid"/>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 表格区域 -->
<!--                :scroll="{ y: tableHeight,x:400 }"  -->
      <div  v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          size="small"
          :height="450"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:props.editConfig.editData.businessType === '1'?totalColumns1:totalColumns2"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination  v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
      <!-- 保存按钮 -->
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '90px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
          <div class="cs-submit-btn merge-3">
            <div style="display: flex;">
              <a-button size="small" :loading="iconLoading" type="primary" @click="handlerSave" class="cs-margin-right">保存</a-button>
              <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            </div>
          </div>
          <!-- 特殊栏位 -->
          <!--          费用类型-->
          <a-form-item name="expenseType" :label="'费用类型'" class="grid-item" :colon="false">
            <cs-select   optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.expenseType" id="expenseType">
              <a-select-option v-for="item in costTypeOptions"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          发票号-->
          <a-form-item name="invoiceNumber" :label="'发票号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.invoiceNumber"/>
          </a-form-item>
<!--          开票用户-->
          <a-form-item name="userMessage" :label="'开票用户'" class="grid-item" :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.expenseType" id="expenseType">
              <a-select-option v-for="item in customerSupplier"  :key="item.label  " :value="item.value" :label=" item.label">
                {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          税额金额-->
          <a-form-item name="taxAmount" :label="'税额金额'" class="grid-item" :colon="false">
            <a-input  size="small" v-model:value="formData.taxAmount" @input="handleTaxAmountInput"
                      @blur="calculateAmount"
                      maxlength="20"
                      placeholder="请输入最多17位整数和2位小数"/>
          </a-form-item>
<!--          无税金额-->
          <a-form-item name="noTaxAmount" :label="'无税金额'" class="grid-item" :colon="false">
            <a-input   size="small" v-model:value="formData.noTaxAmount" @input="handleNoTaxAmountInput"
                       @blur="calculateAmount"
                      maxlength="20"
                      placeholder="请输入最多17位整数和2位小数" />
          </a-form-item>
<!--          费用金额-->
          <a-form-item name="amount" :label="'费用金额'" class="grid-item" :colon="false">
            <a-input :disabled="true"  size="small" v-model:value="formData.amount" @input="handleAmountInput"
                      maxlength="20"
                      placeholder="系统自动计算"/>
          </a-form-item>
<!--          是否分摊-->
          <a-form-item name="apportionment" :label="'是否分摊'" class="grid-item"  :colon="false">
            <cs-select  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.apportionment"  id="apportionment">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label" >
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
<!--          分摊方式-->
          <a-form-item name="methodAllocation" :label="'分摊方式'" class="grid-item"  :colon="false">
            <cs-select  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.methodAllocation" id="methodAllocation">
              <a-select-option v-for="item in productClassify.methodAllocation"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
        </a-form>
      </div>
    </div>


  </section>


</template>

<script setup>

/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
import ContractSearch from "./ShippingOrderSearch.vue";
import {getColumns} from "./ShippingOrderColumns";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";

import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import {
  contractInsert6,
  selectCostType,
  shippingOrderInsert,
  shippingOrderInsert3,
  shippingOrderInsert6,
  shippingOrderInsert7
} from "@/api/cost/cost_message_info";
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import {message} from "ant-design-vue";


const { totalColumns1,totalColumns2 } = getColumns(props.typeMessage)
/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  getList,
  ajaxUrl,
  handlerRefresh,
  gridData

} = useCommon()



defineOptions({
  name: 'ShippingOrderMessage',
});

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },typeMessage: {
    type: Object,
    default: () => {
    }
  },expenseType: {
    type: Object,
    default: () => {
    }
  },
});

const iconLoading = ref(false);

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  console.log('返回', val);
  emit('onEditBack', val);
};

const expenseTypeArr =ref([]);
const formRef = ref(null);
const rules = reactive({
  expenseType: [
    {required: true, message: '费用类型不能为空', trigger: 'blur'},
  ],
  taxAmount: [
    {required: true, message: '税额金额不能为空', trigger: 'blur'},
  ],
  noTaxAmount: [
    {required: true, message: '无税金额不能为空', trigger: 'blur'},
  ],
  apportionment: [
    {required: true, message: '是否分摊不能为空', trigger: 'blur'},
  ],
  methodAllocation: []
});

const formData = reactive({
  expenseType: '',
  invoiceNumber: '',
  userMessage:'',
  invoiceUser: '',
  taxAmount: '',
  noTaxAmount: '',
  amount: '',
  apportionment: '0',
  methodAllocation: '0'
});

const costTypeOptions = ref([]);
const customerSupplier = ref([]);

const handleTaxAmountInput = (event) => {
  const value = event.target.value;
  // 正则表达式：最多19位整数，小数点后最多2位
  const regex = /^\d{0,19}(\.\d{0,2})?$/;
  if (regex.test(value)) {
    formData.taxAmount = value;
  } else {
    // 如果不符合要求，截断为符合要求的格式
    formData.taxAmount = value.slice(0, -1);
  }
};

const handleNoTaxAmountInput = (event) => {
  const value = event.target.value;
  // 正则表达式：最多19位整数，小数点后最多2位
  const regex = /^\d{0,19}(\.\d{0,2})?$/;
  if (regex.test(value)) {
    formData.noTaxAmount = value;
  } else {
    // 如果不符合要求，截断为符合要求的格式
    formData.noTaxAmount = value.slice(0, -1);
  }
};

const handleAmountInput = (event) => {
  const value = event.target.value;
  // 正则表达式：最多19位整数，小数点后最多2位
  const regex = /^\d{0,19}(\.\d{0,2})?$/;
  if (regex.test(value)) {
    formData.amount = value;
  } else {
    // 如果不符合要求，截断为符合要求的格式
    formData.amount = value.slice(0, -1);
  }
};
/* 显示列数据 */
const showColumns =  ref([])
// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
onMounted(fn => {
  ajaxUrl.selectAllPage = ycCsApi.costI.Ilist.shippingOrder.list
  if('6' === props.editConfig.editData.businessType){
    ajaxUrl.selectAllPage = ycCsApi.costI.Ilist.shippingOrder.list6
  }else if ('3' === props.editConfig.editData.businessType){
    ajaxUrl.selectAllPage = ycCsApi.costI.Ilist.shippingOrder.list3
  }else if ('7' === props.editConfig.editData.businessType){
    ajaxUrl.selectAllPage = ycCsApi.costI.Ilist.shippingOrder.list7
  }
  // tableHeight.value = getTableScroll(100,'');
  getList()
  //获取费用类型参数
  //对typeMessage.costTypeOptions对json对象value进行过滤排除expenseType字符串数组
  if(props.expenseType !== null){
    expenseTypeArr.value = props.typeMessage.costTypeOptions.filter(item =>!props.expenseType.includes(item.value));
  }else{
    expenseTypeArr.value = props.typeMessage.costTypeOptions;
  }
})
const tableHeight = ref(500)

/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  if (selectedRowKeys.length > 1) {
    message.warning('只能选择一条记录');
    return;
  }
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
  //费用类型内容制空
  formData.expenseType = null
  costTypeOptions.value=[]
  customerSupplier.value=[]
  formData.invoiceNumber = ''
  //获取费用类型对应的下拉参数
  if(rowSelectData !== null && rowSelectData.length > 0  && rowSelectData){
    formData.invoiceNumber = rowSelectData[0].invoiceNumber
    var params = {
      sid: rowSelectData[0].sid,
      businessType : props.editConfig.editData.businessType,
      type : '2'
    }
    if (selectedRowKeys.length === 1) {
      selectCostType(params).then((res) => {
        res.data.forEach(item => {
            costTypeOptions.value.push({
              value: item.paramCode,
              label: item.costName
            });
            customerSupplier.value.push({
              value: item.paramCode,
              label: item.customerSupplier
            });
          }
        )
      })
    }
  }
};

// 计算费用金额
const calculateAmount = () => {
  console.log("计算费用金额");
  // 将空值或无效值视为 0
  const taxAmount = parseFloat(formData.taxAmount) || 0;
  const noTaxAmount = parseFloat(formData.noTaxAmount) || 0;

  // 计算费用金额
  formData.amount = (taxAmount + noTaxAmount).toFixed(2);
};

/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  if(props.editConfig.editData.businessType === '1'){
    showColumns.value = [...totalColumns1.value]
  }else if (props.editConfig.editData.businessType === '2'){
    showColumns.value = [...totalColumns2.value]
  }else if (props.editConfig.editData.businessType === '6'){
    showColumns.value = [...totalColumns2.value]
  }else if (props.editConfig.editData.businessType === '3'){
    showColumns.value = [...totalColumns2.value]
  }else if (props.editConfig.editData.businessType === '7'){
    showColumns.value = [...totalColumns2.value]
  }
},{deep:true})

// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      iconLoading.value = true;
      if(gridData.selectedRowKeys.length < 1){
        iconLoading.value = false;
        return message.error('请选择进/出货订单!')
      }
      const contractMessage = {
        headId: props.editConfig.editData.sid,
        ...formData,
        businessType :props.editConfig.editData.businessType,
        sids: gridData.selectedRowKeys
      };
      if (props.editConfig.editData.businessType ==='6'){
        shippingOrderInsert6(contractMessage).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }
        })
      }else if (props.editConfig.editData.businessType ==='3'){
        shippingOrderInsert3(contractMessage).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }
        })
      }else if (props.editConfig.editData.businessType ==='7'){
        shippingOrderInsert7(contractMessage).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }
        })
      }
      else {
        shippingOrderInsert(contractMessage).then((res) => {
          if (res.code === 200) {
            message.success('新增成功!')
            onBack(true)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    }).finally(() => {
  })
};
watch(() => formData.apportionment, async (newVal) => {
  await nextTick(); // 等待 DOM 更新

  if (newVal === '0') {
    rules.methodAllocation = [
      { required: true, message: '分摊方式不能为空', trigger: 'blur' }
    ];
  } else {
    rules.methodAllocation = [];
  }

  // 清除该字段的验证状态
  formRef.value?.clearValidate(['methodAllocation']);
}, { immediate: true });

</script>

<style lang="less" scoped>
.grid-item {
  margin: 2px; /* 重置外边距 */
  width: 99%;
}
</style>
