import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  return new Intl.NumberFormat('zh-CN').format(value);
};
export function getColumns(typeMessage) {

  const commColumns = reactive([
    "contractNumber"
    ,"purchaseNumber"
    ,"productName"
    ,"invoiceNumber"
    ,"expenseType"
    ,"quantity"
    ,"taxAmount"
    ,"noTaxAmount"
    ,"expenseAmount"
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    // 合同号
    {
      title: '订单号',
      width: 220,
      align: 'center',
      dataIndex: 'contractNumber',
      key: 'contractNumber',
    },
    // 进货单号
    {
      title: '进货单号',
      width: 220,
      align: 'center',
      dataIndex: 'purchaseNumber',
      key: 'purchaseNumber',
    },
    // 商品名称
    {
      title: '商品名称',
      width: 220,
      align: 'center',
      dataIndex: 'productName',
      key: 'productName',
    },
    // 数量
    {
      title: '数量',
      width: 150,
      align: 'center',
      dataIndex: 'quantity',
      key: 'quantity',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    // 发票号
    {
      title: '发票号',
      width: 220,
      align: 'center',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
    },
    // 费用类型
    {
      title: '费用类型',
      width: 150,
      align: 'center',
      dataIndex: 'expenseType',
      key: 'expenseType',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,typeMessage.costTypeOptions))
      }
    },

    // 税额
    {
      title: '税额',
      width: 150,
      align: 'center',
      dataIndex: 'taxAmount',
      key: 'taxAmount',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    // 无税金额
    {
      title: '无税金额',
      width: 150,
      align: 'center',
      dataIndex: 'noTaxAmount',
      key: 'noTaxAmount',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    // 费用金额
    {
      title: '费用金额',
      width: 150,
      align: 'center',
      dataIndex: 'expenseAmount',
      key: 'expenseAmount',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns,
    commColumns
  }
}


