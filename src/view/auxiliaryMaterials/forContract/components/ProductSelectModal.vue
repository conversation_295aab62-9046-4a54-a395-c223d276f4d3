<template>
  <a-modal
    v-model:visible="props.visible"
    title="选择商品"
    width="1000px"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false"
    :keyboard="false"
    okText="保存"
    cancelText="关闭"
  >
    <!-- 查询条件 -->
    <div class="cs-search">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="商品名称">
          <a-input
            v-model:value="searchForm.productName"
            placeholder="请输入商品名称"
            allow-clear
            style="width: 200px"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 商品列表 -->
    <div class="table-container" :style="{ minHeight: tableHeight + 'px' }">
      <s-table
        ref="productTableRef"
        class="cs-action-item"
        size="small"
        height="50vh"
        :scroll="{ y: tableHeight }"
        bordered
        :columns="columns"
        :data-source="productData"
        :row-key="getRowKey"
        :pagination="false"
        :loading="loading"
        :row-selection="{
          selectedRowKeys: selectedKeys,
          onChange: onSelectChange
        }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'weight'">
            {{ formatNumberNew(record.weight) }}
          </template>
          <template v-if="column.dataIndex === 'unit'">
            {{ formatUnit(record.unit) }}
          </template>
          <template v-if="column.dataIndex === 'supplier'">
            {{ sellerValueFormat(record.supplier) }}
          </template>
        </template>
      </s-table>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import { useColumnsRender } from '@/view/common/useColumnsRender';
import {useMerchant} from "@/view/common/useMerchant";

const { formatNumberNew } = useColumnsRender();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderNo: {
    type: String,
    default: ''
  },
  unitOptions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'select']);


const formatUnit = (code) => {
  const option = props.unitOptions.find(opt => opt.value === code);
  return option ? option.label : code;
};

const { cmbShowRender } = useColumnsRender();

// 获取供应商数据
const { merchantOptions, getMerchantOptions } = useMerchant()

const sellerValueFormat = (value) => {
  if (!value) return '';
  return cmbShowRender(value, merchantOptions.value);
}

// 商品列表数据
const productData = ref([]);
const selectedKeys = ref([]);
const selectedProducts = ref([]);
const loading = ref(false);
const tableHeight = ref(300);

// 查询表单
const searchForm = reactive({
  productName: '',
  productModel: '',
  specification: ''
});

// 列定义
const columns = [
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 180,
    align: 'center'
  },
  {
    title: '产品型号',
    dataIndex: 'productModel',
    width: 150,
    align: 'center'
  },
  {
    title: '规格',
    dataIndex: 'specification',
    width: 150,
    align: 'center'
  },
  {
    title: '克重',
    dataIndex: 'weight',
    width: 120,
    align: 'center',
    customRender: ({ text }) => {
      return formatNumberNew(text);
    }
  },
  {
    title: '供应商',
    dataIndex: 'supplier',
    width: 200,
    align: 'center'
  },
];




// 生成行唯一标识
const getRowKey = (record) => {
  return record.id;
};

// 获取商品列表
const getProductList = async () => {
  loading.value = true;
  try {
    const params = {
      productName: searchForm.productName,
      businessType: '2',
      orderNo: props.orderNo
    };
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.auxiliaryMaterials.orderNotice.body.list}`,
      params
    );
    if (res.code === 200) {
      productData.value = res.data || [];
    } else {
      productData.value = [];
      message.error(res.message || '获取商品列表失败');
    }
  } catch (error) {
    productData.value = [];
    message.error('获取商品列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  selectedKeys.value = [];
  selectedProducts.value = [];
  getProductList();
};

// 处理重置
const handleReset = () => {
  searchForm.gName = '';
  selectedKeys.value = [];
  selectedProducts.value = [];
  getProductList();
};

// 处理选择
const onSelectChange = (selectedRowKeys, selectedRows) => {
  selectedKeys.value = selectedRowKeys;
  selectedProducts.value = selectedRows;
};

// 处理确认
const handleOk = () => {
  if (selectedProducts.value.length === 0) {
    message.warning('请选择至少一个商品');
    return;
  }
  try {
    emit('select', selectedKeys.value);
    handleCancel();
  } catch (error) {
    console.error('选择商品失败', error)
  }
};

// 处理取消
const handleCancel = () => {
  selectedKeys.value = [];
  selectedProducts.value = [];
  productData.value = [];
  emit('update:visible', false);
};

// 监听visible变化，当显示时加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 设置表格高度
    tableHeight.value = 300;
    getProductList();
  } else {
    productData.value = [];
    selectedKeys.value = [];
    selectedProducts.value = [];
  }
});

// 组件卸载时清理数据
onMounted(() => {
  productData.value = [];
  selectedKeys.value = [];
  selectedProducts.value = [];
  getMerchantOptions()
});
</script>

<style lang="less" scoped>
.cs-search {
  margin-bottom: 16px;
}

.table-container {
  position: relative;
  min-height: 300px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  color: #999;
  font-size: 14px;
}

:deep(.ant-table-wrapper) {
  .ant-table {
    .ant-table-row {
      &.ant-table-row-selected > td {
        background-color: #e6f7ff;
      }
      &:hover > td {
        background-color: #fafafa;
      }
    }
  }
}

:deep(.ant-radio-wrapper) {
  .ant-radio-checked {
    .ant-radio-inner {
      border-color: #1890ff;
      &::after {
        background-color: #1890ff;
      }
    }
  }
}
</style>
