import {deleteAction, getAction, postAction, putAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";



/*进口费用*/
export const insertCostHead = (params) =>window.majesty.httpUtil.postAction(ycCsApi.costI.insert, params)
export const updateCostHead = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.costI.update}/${sid}`, params)
export const deleteCostHead = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.costI.delete}/${sids}`)
export const costIHeadChargeback = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.chargeback}/${sids}`)
export const costIHeadcancellation = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.cancellation}/${sids}`)
export const costIHeadcanCopy = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.copy}/${sids}`)
export const costIHeadAffirm = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.affirm}/${sids}`)


export const selectCostList = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.list}`,params)
export const UpdateCostList = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.costI.Ilist.update}/${sid}`, params)
export const deleteCostList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.costI.Ilist.delete}/${sids}`)
export const getSumDataCost = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.getSumDataCost}`,params)

export const contractList = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.IContract.list}`,params)
export const contractInsert = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.IContract.insert}`,params)
export const contractInsert6 = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.IContract.insert6}`,params)
export const contractInsert3 = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.IContract.insert3}`,params)
export const contractInsert7 = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.IContract.insert7}`,params)
export const selectCostType = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.IContract.selectCostType}`,params)
export const shippingOrderInsert = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.shippingOrder.insert}`,params)
export const shippingOrderInsert6 = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.shippingOrder.insert6}`,params)
export const shippingOrderInsert3 = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.shippingOrder.insert3}`,params)
export const shippingOrderInsert7 = (params) => window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.shippingOrder.insert7}`,params)
